'use client'
import {
  Carousel,
  Carousel<PERSON>pi,
  CarouselContent,
  CarouselItem,
} from '@/components/ui/carousel'
import { BoundingBoxGroup, GetPostResponse, Role } from '@/types'
import { useCallback, useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { VideoPlayer } from '../VideoPlayer'
import { ImageWithBoundingBox } from '../ImageWithBoundingBoxes'
import { usePost } from '@/context'
import PostHeader from '../PostHeader'
import ImageCrop from '../ImageCrop'

type TPostImagesProps = {
  dataPost: GetPostResponse
  boundingBoxGroup?: BoundingBoxGroup[]
  role?: Role
}

export function PostImages({
  dataPost,
  boundingBoxGroup,
  role,
}: TPostImagesProps) {
  const [carrousel, setCarrousel] = useState<CarouselApi>()
  const { replace } = useRouter()
  const { isAddingBoundingBox } = usePost()
  const searchParams = useSearchParams()
  const image = searchParams.get('image')
  const post = dataPost.post
  const medias = post.medias
  const photoUrl = post.influencer.user.profileImg
  const influencerUsername = post.influencer.user.username
  const isAdmin = role === 'ADMIN'
  const isOwner = post.isOwner
  const canEdit = isAdmin || isOwner

  // Log post data with isOwner information
  useEffect(() => {
    console.log(
      '[PostImages] Post data:',
      JSON.stringify(
        {
          postId: post.id,
          isOwner: post.isOwner,
          role,
          canEdit,
          influencerUsername,
          authStatus: 'Checking auth in component',
        },
        null,
        2,
      ),
    )

    // Log more details about the post and auth state
    console.log('[PostImages] Detailed post auth info:', {
      postId: post.id,
      isOwner: post.isOwner,
      role,
      isAdmin,
      canEdit,
      mediaCount: medias.length,
    })
  }, [
    post.id,
    post.isOwner,
    role,
    canEdit,
    influencerUsername,
    isAdmin,
    medias.length,
  ])
  const mediasWithBoundingBox = medias.map((media) => {
    const boundingBox = boundingBoxGroup?.filter(
      (item) => item.mediaId === media.id,
    )
    return {
      ...media,
      boundingBox,
    }
  })

  const initialIndex = medias.findIndex((media) => media.id === image)
  const safeInitialIndex = initialIndex === -1 ? 0 : initialIndex

  useEffect(() => {
    const currentParams = new URLSearchParams(searchParams.toString())
    currentParams.set('image', medias[safeInitialIndex].id)
    replace(`?${currentParams.toString()}`)
  }, [safeInitialIndex, medias, replace, searchParams])

  useEffect(() => {
    if (!carrousel) {
      return
    }
    carrousel.scrollTo(safeInitialIndex)
  }, [carrousel, safeInitialIndex])

  const handleChangeImage = useCallback(
    (index: number) => {
      const currentParams = new URLSearchParams(searchParams.toString())
      currentParams.set('image', medias[index].id)
      currentParams.delete('selectedId')
      replace(`?${currentParams.toString()}`)
    },
    [medias, replace, searchParams],
  )

  useEffect(() => {
    if (!carrousel) {
      return
    }
    carrousel.on('select', () => {
      handleChangeImage(carrousel.selectedScrollSnap())
    })
  }, [carrousel, medias, handleChangeImage])

  const firstMediaIsVideo = medias[0].isVideo
  const hasOnlyOneMedia = medias.length === 1

  return (
    <div>
      <PostHeader
        photoUrl={photoUrl}
        influencerUsername={influencerUsername}
        canEdit={canEdit}
      />
      {hasOnlyOneMedia && firstMediaIsVideo && (
        <VideoPlayer
          src={mediasWithBoundingBox[0].mediaUrl}
          className="mx-auto h-96 w-96 rounded-xl lg:h-[450px] lg:w-[450px] xl:h-[500px] xl:w-[500px]"
        />
      )}

      {hasOnlyOneMedia && !firstMediaIsVideo && (
        <div className="mt-6 md:w-[384px] lg:w-[450px] xl:w-[500px]">
          {isAddingBoundingBox ? (
            <ImageCrop mediaUrl={mediasWithBoundingBox[0].mediaUrl} />
          ) : (
            <ImageWithBoundingBox
              alt="Post Image"
              src={mediasWithBoundingBox[0].mediaUrl}
              width={380}
              height={380}
              className="mx-auto h-96 w-96 rounded-xl object-cover lg:h-[450px] lg:w-[450px] xl:h-[500px] xl:w-[500px]"
              unoptimized={true}
              boundingBoxGroup={mediasWithBoundingBox[0].boundingBox}
            />
          )}
        </div>
      )}

      {medias.length > 1 && (
        <Carousel
          className="mt-6 md:w-[384px] lg:w-[450px] xl:w-[500px]"
          setApi={setCarrousel}
        >
          <CarouselContent>
            {mediasWithBoundingBox.map((media) => (
              <CarouselItem key={media.id}>
                {media.isVideo ? (
                  <VideoPlayer
                    src={media.mediaUrl}
                    className="mx-auto h-96 w-96 rounded-xl lg:h-[450px] lg:w-[450px] xl:h-[500px] xl:w-[500px]"
                  />
                ) : isAddingBoundingBox ? (
                  <ImageCrop mediaUrl={media.mediaUrl} />
                ) : (
                  <ImageWithBoundingBox
                    alt="Post Image"
                    src={media.mediaUrl}
                    width={380}
                    height={380}
                    className="mx-auto h-96 w-96 rounded-xl object-cover lg:h-[450px] lg:w-[450px] xl:h-[500px] xl:w-[500px]"
                    unoptimized={true}
                    boundingBoxGroup={media.boundingBox}
                  />
                )}
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      )}
    </div>
  )
}
